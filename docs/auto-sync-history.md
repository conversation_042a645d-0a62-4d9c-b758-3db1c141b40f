# Auto Sync History Feature

## Tổng quan

Tính năng Auto Sync History tự động đồng bộ trạng thái của các tác vụ generation (text-to-image, text-to-video, text-to-speech, dialog-to-speech) từ server để cập nhật trạng thái real-time cho người dùng.

## Cách hoạt động

### 1. <PERSON><PERSON> nào được kích hoạt
- Sau khi gọi thành công các API generation
- Chỉ khi response trả về `history_uuid`
- Tự động bắt đầu sync ngay sau khi tạo task

### 2. <PERSON><PERSON><PERSON> hình mặc định
- **Interval**: 30 giây (gọi syncHistory mỗi 30s)
- **Max Duration**: 5 phút (tối đa sync trong 5 phút)
- **Target Status**: [2, 3] (Complete hoặc Error)
- **Auto Stop**: Dừng khi đạt target status hoặc hết thời gian

### 3. <PERSON>ồng hoạt động
```
1. User tạo generation request
2. <PERSON> tr<PERSON> về history_uuid
3. Auto sync bắt đầu
4. G<PERSON>i syncHistory mỗi 30s
5. Cập nhật result trong store
6. Dừng khi status = 2 hoặc 3, hoặc sau 5 phút
```

## Implementation

### Composable: useAutoSyncHistory

```typescript
const { startAutoSync, stopAutoSync, isAutoSyncing } = useAutoSyncHistory()

startAutoSync({
  uuid: 'history-uuid',
  intervalMs: 30000,
  maxDurationMs: 300000,
  targetStatuses: [2, 3],
  onStatusChange: (status, historyDetail) => {
    // Cập nhật UI khi status thay đổi
  },
  onComplete: (historyDetail) => {
    // Xử lý khi hoàn thành
  },
  onError: (error) => {
    // Xử lý lỗi
  }
})
```

### Tích hợp trong Stores

#### Text to Image Store
```typescript
// Trong textToImage action
if (responseData.history_uuid) {
  const { startAutoSync } = useAutoSyncHistory()
  startAutoSync({
    uuid: responseData.history_uuid,
    onStatusChange: (status, historyDetail) => {
      this.textToImageResult = {
        ...this.textToImageResult,
        ...historyDetail
      }
    }
  })
}
```

#### Text to Video Store
```typescript
// Trong textToVideo action
if (response.data.history_uuid) {
  const { startAutoSync } = useAutoSyncHistory()
  startAutoSync({
    uuid: response.data.history_uuid,
    onStatusChange: (status, historyDetail) => {
      this.textToVideoResult = {
        ...this.textToVideoResult,
        ...historyDetail
      }
    }
  })
}
```

#### Text to Speech Store
```typescript
// Trong textToSpeech và documentToSpeech actions
if (response.data.history_uuid) {
  const { startAutoSync } = useAutoSyncHistory()
  startAutoSync({
    uuid: response.data.history_uuid,
    onStatusChange: (status, historyDetail) => {
      this.textToSpeechResult = {
        ...this.textToSpeechResult,
        ...historyDetail
      }
    }
  })
}
```

#### Dialog to Speech Store
```typescript
// Trong generateDialogSpeech action
if (response.data.history_uuid) {
  const { startAutoSync } = useAutoSyncHistory()
  startAutoSync({
    uuid: response.data.history_uuid,
    onStatusChange: (status, historyDetail) => {
      this.dialogResult = {
        ...this.dialogResult,
        ...historyDetail
      }
    }
  })
}
```

## Status Codes

- **0**: Pending/Queued
- **1**: Processing
- **2**: Complete/Success
- **3**: Error/Failed

## Features

### 1. Automatic Management
- Tự động bắt đầu sau generation
- Tự động dừng khi đạt target status
- Tự động cleanup khi component unmount

### 2. Error Handling
- Tiếp tục sync ngay cả khi có lỗi
- Log chi tiết các lỗi
- Callback để xử lý lỗi custom

### 3. Performance
- Không sync trùng lặp cho cùng UUID
- Cleanup timers khi không cần
- Giới hạn thời gian tối đa

### 4. Debugging
- Console logs chi tiết
- Test page tại `/test-auto-sync`
- Tracking active syncs

## Testing

### Test Page
Truy cập `/test-auto-sync` để:
- Xem danh sách active syncs
- Test với UUID tùy ý
- Xem logs real-time
- Stop syncs manually

### Manual Testing
1. Tạo generation request
2. Kiểm tra console logs
3. Verify result được cập nhật
4. Confirm sync dừng khi complete

## Best Practices

### 1. Error Handling
```typescript
onError: (error) => {
  console.error('Sync error:', error)
  // Có thể show notification cho user
  // Không cần stop sync, sẽ tự retry
}
```

### 2. Status Updates
```typescript
onStatusChange: (status, historyDetail) => {
  // Cập nhật store state
  this.result = { ...this.result, ...historyDetail }
  
  // Có thể cập nhật progress bar
  if (historyDetail.status_percentage) {
    this.progress = historyDetail.status_percentage
  }
}
```

### 3. Completion Handling
```typescript
onComplete: (historyDetail) => {
  // Final update
  this.result = { ...this.result, ...historyDetail }
  
  // Show success notification
  // Update UI state
  // Trigger any post-completion actions
}
```

## Troubleshooting

### Common Issues

1. **Sync không bắt đầu**
   - Kiểm tra `history_uuid` có tồn tại
   - Verify API response format
   - Check console logs

2. **Sync không dừng**
   - Kiểm tra target statuses
   - Verify server trả về đúng status
   - Check max duration setting

3. **Performance issues**
   - Giảm interval nếu cần
   - Kiểm tra memory leaks
   - Monitor active syncs count

### Debug Commands
```javascript
// Trong browser console
const { getActiveSyncs, stopAllAutoSyncs } = useAutoSyncHistory()
console.log('Active syncs:', getActiveSyncs())
stopAllAutoSyncs() // Emergency stop
```
