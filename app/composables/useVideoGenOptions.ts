export const useVideoGenOptions = () => {
  const { model } = useVideoGenModels()

  // Duration based on model
  const duration = useCookie<number>('video-gen-duration', {
    default: () => model.value?.duration?.default || 8
  })

  // Default person generation
  const personGeneration = ref('ALLOW_ALL')

  // Aspect ratio options (kept for backward compatibility, but use videoDimensions instead)
  const aspectRatioOptions = [
    { label: '16:9 (Landscape)', value: '16:9' },
    { label: '9:16 (Portrait)', value: '9:16' },
    { label: '1:1 (Square)', value: '1:1' },
    { label: '4:3 (Standard)', value: '4:3' },
    { label: '3:4 (Portrait)', value: '3:4' }
  ]

  // Default aspect ratio
  const aspectRatio = ref('16:9')

  // Enhance prompt option - reactive based on model
  const enhancePrompt = useCookie<boolean>('video-gen-enhance-prompt', {
    default: () => model.value?.enhancePrompt?.default || true
  })

  // Duration options based on current model
  const durationOptions = computed(() => {
    const options = model.value?.duration?.options || [8]
    return options.map((seconds: number) => ({
      label: `${seconds}s`,
      value: seconds
    }))
  })

  // Check if enhance prompt is locked for current model
  const isEnhancePromptLocked = computed(() => {
    return model.value?.enhancePrompt?.locked || false
  })

  // Check if duration selection is available for current model
  const isDurationSelectable = computed(() => {
    const options = model.value?.duration?.options || [8]
    return options.length > 1
  })

  // Watch model changes to update defaults
  watch(model, (newModel) => {
    if (newModel) {
      // Update enhance prompt if not locked
      if (newModel.enhancePrompt?.locked) {
        enhancePrompt.value = newModel.enhancePrompt.default
      }

      // Update duration to model default if current value not available
      const availableOptions = newModel.duration?.options || [8]
      if (!availableOptions.includes(duration.value)) {
        duration.value = newModel.duration?.default || availableOptions[0]
      }
    }
  }, { immediate: true })

  return {
    // Duration
    duration,
    durationOptions,
    isDurationSelectable,

    // Person generation
    personGeneration,

    // Aspect ratio (backward compatibility)
    aspectRatio,
    aspectRatioOptions,

    // Enhance prompt
    enhancePrompt,
    isEnhancePromptLocked
  }
}
