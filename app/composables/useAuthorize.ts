import BaseModal from '~/components/base/BaseModal.vue'

export function useAuthorize() {
  const authStore = useAuthStore()
  const router = useRouter()
  const overlay = useOverlay()
  const modal = overlay.create(BaseModal)
  const { t } = useI18n()
  const { isAuthenticated, isNotVerifyAccount, user_credit, isPremiumUser, isFreeUser }
    = storeToRefs(authStore)

  // Define plan types
  type PlanType = 'free' | 'premium' | 'any'

  const authorize = (callback: () => void, requiredPlan?: PlanType) => {
    if (!isAuthenticated.value) {
      router.push('/auth/login')
      return
    }
    // check if user inactive
    if (isNotVerifyAccount.value) {
      modal.open({
        title: t('Your account is not verified'),
        description: t(
          'Your account is not verified. Please verify your account to continue'
        )
      })
      return
    }
    // check if user has enough credit
    if (user_credit.value?.available_credit < 1) {
      modal.open({
        title: t('Not enough credit'),
        description: t('Your account does not have enough credit. Please top up your account to continue.'),
        buttons: [
          {
            label: t('Top up now'),
            color: 'primary',
            trailingIcon: 'ep:right',
            onClick: () => {
              router.push('/profile/credits')
              modal.close()
            }
          }
        ]
      })
      return
    }

    // check plan requirement if specified
    if (requiredPlan && requiredPlan !== 'any') {
      const userHasRequiredPlan = checkUserPlan(requiredPlan)
      if (!userHasRequiredPlan) {
        modal.open({
          title: t('Plan upgrade required'),
          description: t(`This feature requires ${requiredPlan} plan. Please upgrade your plan to continue.`),
          buttons: [
            {
              label: t('Upgrade plan'),
              color: 'primary',
              trailingIcon: 'ep:right',
              onClick: () => {
                router.push('/pricing')
                modal.close()
              }
            }
          ]
        })
        return
      }
    }

    callback()
  }

  // Helper function to check user plan
  const checkUserPlan = (requiredPlan: PlanType): boolean => {
    switch (requiredPlan) {
      case 'free':
        return !!isFreeUser.value || !!isPremiumUser.value // Premium users can access free features
      case 'premium':
        return !!isPremiumUser.value
      case 'any':
        return true
      default:
        return false
    }
  }

  return {
    authorize,
    checkUserPlan
  }
}
