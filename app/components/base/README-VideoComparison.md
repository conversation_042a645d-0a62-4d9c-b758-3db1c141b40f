# Video Comparison Components

Các component để hiển thị so sánh giữa input (text/image) và video được tạo bởi AI.

## Components

### 1. BaseVideoComparisonDemo.vue
Component demo đơn giản với placeholder, không cần video thực.

**Features:**
- So sánh Text-to-Video và Image-to-Video
- Draggable slider
- Auto-cycling examples
- Responsive design
- Loading states

**Usage:**
```vue
<BaseVideoComparisonDemo :height="'350px'" />
```

### 2. BaseVideoComparisonExamples.vue
Component đầy đủ với video thực và tutorial.

**Features:**
- Hỗ trợ video thực
- Tutorial cho lần đầu sử dụng
- Autoplay controls
- Navigation arrows
- Dots indicator

**Usage:**
```vue
<BaseVideoComparisonExamples
  :height="'350px'"
  :autoplay="true"
  :interval="8000"
/>
```

### 3. BaseVideoComparisonSlider.vue
Component slider c<PERSON> bản để so sánh content.

**Props:**
- `beforeContent`: Text prompt hoặc image URL
- `afterContent`: Video URL
- `beforeType`: 'text' | 'image'
- `afterType`: 'video'
- `beforeLabel`: Label cho content trước
- `afterLabel`: Label cho content sau
- `height`: Chiều cao component
- `initialPosition`: Vị trí ban đầu của slider (0-100)

## Integration trong video-gen.vue

Component được tích hợp vào trang video-gen.vue để hiển thị khi chưa có kết quả generation:

```vue
<UCard v-else>
  <div class="h-full">
    <div class="mb-4 text-center">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {{ $t('AI Video Generation Examples') }}
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ $t('Explore the power of AI video generation with these interactive comparisons') }}
      </p>
    </div>
    <BaseVideoComparisonDemo :height="'350px'" />
  </div>
</UCard>
```

## Internationalization

Các text đã được thêm vào i18n:

**Tiếng Việt (vi.json):**
- "AI Video Generation Examples": "Ví dụ tạo video AI"
- "Explore the power of AI video generation with these interactive comparisons": "Khám phá sức mạnh của việc tạo video AI với những so sánh tương tác này"
- "Try the Video Comparison!": "Thử so sánh video!"
- "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "Kéo thanh trượt qua trái và phải để so sánh gợi ý văn bản/hình ảnh với video được tạo. Bạn cũng có thể nhấp vào bất kỳ đâu để di chuyển thanh trượt."

**Tiếng Anh (en.json):**
- Tương tự với bản tiếng Việt

## Examples Data

### Text-to-Video Examples:
1. "A majestic eagle soaring through mountain peaks at sunset, golden hour lighting, cinematic shot"
2. "A peaceful forest with sunlight filtering through trees, gentle breeze moving leaves"

### Image-to-Video Examples:
1. Mountain landscape → Animated video
2. Ocean waves → Time-lapse animation

## Technical Notes

- Component sử dụng clip-path CSS để tạo hiệu ứng slider
- Hỗ trợ mouse và touch events
- Auto-play và navigation controls
- Error handling cho video loading
- TypeScript support với proper interfaces
- Responsive design với Tailwind CSS

## Future Enhancements

1. Thêm video thực từ API
2. Lazy loading cho video
3. Thumbnail preview
4. Video controls (play/pause)
5. Fullscreen mode
6. Share functionality
