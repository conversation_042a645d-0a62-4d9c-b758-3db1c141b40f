<template>
  <UModal
    v-model:open="isOpen"
    :ui="{
      wrapper: 'max-h-[90vh] overflow-auto !scrollbar-thin'
    }"
  >
    <template #content>
      <UForm
        ref="formRef"
        :schema="schema"
        :state="formData"
        @submit="onSubmit"
      >
        <UCard
          :ui="{
            body: 'max-h-[75vh] overflow-y-auto p-6 !scrollbar-thin'
          }"
        >
          <template
            v-if="currentStep === 2"
            #header
          >
            <div class="flex flex-row justify-between items-center">
              <div class="flex items-center space-x-2">
                <UIcon
                  name="fluent:person-voice-24-filled"
                  class="w-5 h-5 text-primary-500"
                />
                <h2 class="text-xl font-semibold">
                  {{ $t("Create Custom Voice") }}
                </h2>
              </div>
              <UButton
                color="neutral"
                variant="ghost"
                icon="i-lucide-x"
                @click="closeModal"
              />
            </div>
          </template>

          <!-- Step 1: Voice Type Selection -->
          <div
            v-if="currentStep === 1"
            class="space-y-6"
          >
            <div class="text-center">
              <h3 class="text-lg font-medium mb-4">
                {{ $t("Type of voice to create") }}
              </h3>
            </div>

            <div class="grid grid-cols-1 gap-4">
              <!-- Instant Voice Cloning -->
              <div
                class="border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                :class="{
                  'border-primary-500 bg-primary-50 dark:bg-primary-900/20':
                    selectedTrainingType === 'instant_voice',
                  'border-gray-200 dark:border-gray-700':
                    selectedTrainingType !== 'instant_voice'
                }"
                @click="selectedTrainingType = 'instant_voice'"
              >
                <div class="flex items-start space-x-3">
                  <URadio
                    v-model="selectedTrainingType"
                    value="instant_voice"
                    class="mt-1"
                  />
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {{ $t("Instant Voice Cloning") }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {{
                        $t(
                          "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise"
                        )
                      }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Professional Voice Cloning -->
              <div
                class="border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                :class="{
                  'border-primary-500 bg-primary-50 dark:bg-primary-900/20':
                    selectedTrainingType === 'professional_voice',
                  'border-gray-200 dark:border-gray-700':
                    selectedTrainingType !== 'professional_voice'
                }"
                @click="selectedTrainingType = 'professional_voice'"
              >
                <div class="flex items-start space-x-3">
                  <URadio
                    v-model="selectedTrainingType"
                    value="professional_voice"
                    class="mt-1"
                  />
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {{ $t("Professional Voice Cloning") }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {{
                        $t(
                          "Create the most realistic digital replica of your voice."
                        )
                      }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-2">
              <UButton
                variant="outline"
                @click="closeModal"
              >
                {{ $t("Cancel") }}
              </UButton>
              <UButton
                :disabled="!selectedTrainingType"
                @click="nextStep"
              >
                {{ $t("Next") }}
              </UButton>
            </div>
          </div>

          <!-- Step 2: Voice Details Form -->
          <div
            v-else-if="currentStep === 2"
            class="space-y-4"
          >
            <div class="space-y-3">
              <!-- Speaker Name -->
              <UFormField
                :label="$t('Speaker Name')"
                name="speaker_name"
                required
              >
                <UInput
                  v-model="formData.speaker_name"
                  :placeholder="$t('Enter speaker name')"
                  class="w-full"
                  size="sm"
                />
              </UFormField>

              <!-- Description -->
              <UFormField
                :label="$t('Description')"
                name="description"
              >
                <UTextarea
                  v-model="formData.description"
                  :placeholder="$t('Describe the voice characteristics')"
                  :rows="3"
                  class="w-full"
                  size="sm"
                />
              </UFormField>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
                <!-- Gender -->
                <UFormField
                  :label="$t('Gender')"
                  name="gender"
                  required
                >
                  <USelect
                    v-model="formData.gender"
                    :items="genderOptions"
                    option-attribute="label"
                    value-attribute="value"
                    :placeholder="$t('Select gender')"
                    class="w-full"
                    size="sm"
                  />
                </UFormField>

                <!-- Age -->
                <UFormField
                  :label="$t('Age')"
                  name="age"
                  required
                >
                  <USelect
                    v-model="formData.age"
                    :items="ageOptions"
                    option-attribute="label"
                    value-attribute="value"
                    :placeholder="$t('Select age')"
                    class="w-full"
                    size="sm"
                  />
                </UFormField>

                <!-- Accent -->
                <UFormField
                  :label="$t('Accent')"
                  name="accent"
                  required
                >
                  <USelectMenu
                    v-model="formData.accent"
                    :items="accentOptions"
                    placeholder="Country"
                    value-key="value"
                    size="sm"
                    class="w-full md:min-w-32"
                    :ui="{
                      content: 'w-52'
                    }"
                  />
                </UFormField>
              </div>

              <!-- Audio File Upload -->
              <UFormField
                :label="$t('Audio Sample')"
                name="audioFile"
                required
              >
                <!-- Notes -->
                <div
                  class="mb-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg px-4 py-2"
                >
                  <div class="flex items-start space-x-2">
                    <UIcon
                      name="heroicons:information-circle"
                      class="w-5 h-5 text-orange-500 mt-0.5"
                    />
                    <div class="text-sm text-orange-700 dark:text-orange-300">
                      <p class="font-medium mb-1">
                        {{ $t("Note:") }}
                      </p>
                      <ul class="space-y-1 text-xs">
                        <li>
                          {{
                            $t(
                              "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality."
                            )
                          }}
                        </li>
                        <li>
                          {{
                            $t(
                              "It will cost 0 credits each time you create a voice."
                            )
                          }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <BaseFileSelect
                  v-if="!selectedAudioFile"
                  v-model="audioFiles"
                  :support-files="audioSupportFiles"
                  :support-files-display="audioSupportFilesDisplay"
                  @update:model-value="handleAudioFileSelected"
                />
                <div
                  v-else
                  class="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4"
                >
                  <div class="flex items-center space-x-3">
                    <UIcon
                      name="heroicons:musical-note"
                      class="w-6 h-6 text-primary-500"
                    />
                    <div class="flex-1">
                      <p class="text-sm font-medium">
                        {{ selectedAudioFile.name }}
                      </p>
                      <p class="text-xs text-gray-500">
                        {{ formatFileSize(selectedAudioFile.size) }}
                      </p>
                    </div>
                    <UButton
                      icon="heroicons:x-mark"
                      size="xs"
                      color="neutral"
                      variant="ghost"
                      @click="removeAudioFile"
                    />
                  </div>
                </div>
              </UFormField>

              <!-- Privacy Policy Checkbox -->
            </div>
          </div>

          <template
            v-if="currentStep === 2"
            #footer
          >
            <div class="flex flex-row justify-between items-center">
              <UFormField name="agreeToPrivacy">
                <UCheckbox
                  v-model="formData.agreeToPrivacy"
                  :label="$t('I agree to the privacy policy')"
                />
              </UFormField>
              <div class="flex justify-end space-x-2">
                <UButton
                  variant="outline"
                  @click="previousStep"
                >
                  {{ $t("Back") }}
                </UButton>
                <UButton
                  type="submit"
                  :loading="isLoading"
                  :disabled="!isFormValid"
                >
                  {{ $t("Generate") }}
                </UButton>
              </div>
            </div>
          </template>
        </UCard>
      </UForm>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { z } from 'zod'

const { voiceAccents } = useSpeechVoices()
interface Props {
  modelValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'voiceCreated': [voice: any]
}>()

const { t } = useI18n()
const textToSpeechStore = useTextToSpeechStore()

// Modal state
const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

// Form state
const currentStep = ref(1)
const selectedTrainingType = ref('')
const formRef = ref()
const audioFiles = ref<File[]>([])
const selectedAudioFile = computed(() => audioFiles.value[0] || null)

// Form data
const formData = ref({
  speaker_name: '',
  description: '',
  gender: '',
  age: '',
  accent: '',
  agreeToPrivacy: false
})

// Loading state
const isLoading = computed(
  () => textToSpeechStore.loadings.createCustomVoice || false
)

// Audio file support
const audioSupportFiles = [
  'mp3',
  'wav',
  'ogg',
  'aac',
  'm4a',
  'flac',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg',
  'audio/aac',
  'audio/mp4',
  'audio/flac'
]
const audioSupportFilesDisplay = ['MP3', 'WAV', 'OGG', 'AAC', 'M4A', 'FLAC']

// Options
const genderOptions = computed(() => [
  { value: 'Male', label: t('Male') },
  { value: 'Female', label: t('Female') }
])

const ageOptions = [
  { label: t('Young'), value: 'Young' },
  { label: t('Middle'), value: 'Middle Aged' },
  { label: t('Old'), value: 'Old' }
]

const accentOptions = computed(() => {
  const accents = voiceAccents().map(accent => ({
    value: accent.value,
    label: accent.label,
    icon: accent.icon
  }))
  return [...accents]
})

// Form validation schema
const schema = computed(() =>
  z.object({
    speaker_name: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    description: z.string(),
    gender: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    age: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    accent: z
      .string({
        required_error: t('validation.required')
      })
      .min(1, t('validation.required')),
    agreeToPrivacy: z.boolean().refine(val => val === true, {
      message: t('validation.mustAgreeToPrivacy')
    })
  })
)

// Computed properties
const isFormValid = computed(() => {
  return (
    formData.value.speaker_name
    && selectedAudioFile.value
    && formData.value.agreeToPrivacy
    && formData.value.gender
    && formData.value.age
    && formData.value.accent
  )
})

// Methods
const nextStep = () => {
  if (selectedTrainingType.value) {
    currentStep.value = 2
  }
}

const previousStep = () => {
  currentStep.value = 1
}

const closeModal = () => {
  isOpen.value = false
  resetForm()
}

const resetForm = () => {
  currentStep.value = 1
  selectedTrainingType.value = ''
  formData.value = {
    speaker_name: '',
    description: '',
    gender: '',
    age: '',
    accent: '',
    agreeToPrivacy: false
  }
  audioFiles.value = []
}

const handleAudioFileSelected = (files: File[]) => {
  audioFiles.value = files
}

const removeAudioFile = () => {
  audioFiles.value = []
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const onSubmit = async () => {
  if (!selectedAudioFile.value) return

  try {
    textToSpeechStore.loadings['createCustomVoice'] = true
    // Upload audio file first
    const uploadUrlRes = await textToSpeechStore.getUploadFileUrl(
      selectedAudioFile.value
    )
    await textToSpeechStore.uploadFile(
      selectedAudioFile.value,
      uploadUrlRes.url
    )

    // Create custom voice
    const payload = {
      speaker_name: formData.value.speaker_name,
      audio_path: uploadUrlRes.s3_file_path,
      description: formData.value.description,
      training_type: selectedTrainingType.value,
      gender: formData.value.gender,
      age: formData.value.age,
      accent: formData.value.accent
    }

    const result = await textToSpeechStore.createCustomVoice(payload)

    if (result) {
      emit('voiceCreated', result)
      // voices.value.unshift(result)
      useSpeechVoicesStore().addVoice(result)
      closeModal()

      // Show success notification
      const toast = useToast()
      toast.add({
        title: t('Success'),
        description: t('Custom voice created successfully'),
        color: 'success'
      })
    }
  } catch (error) {
    console.error('Error creating custom voice:', error)
    const toast = useToast()
    toast.add({
      title: t('Error'),
      description: t('Failed to create custom voice'),
      color: 'error'
    })
  }
}

// Watch for modal close to reset form
watch(isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>
