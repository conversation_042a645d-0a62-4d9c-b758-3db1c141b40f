<script setup lang="ts">
const authStore = useAuthStore()
const { isNotVerifyAccount, canResendActivationEmailAfter, user }
  = storeToRefs(authStore)

const resendActivationEmailInterval = ref(null as any)

const appStore = useAppStore()
const { adsBanner } = storeToRefs(appStore)

// Refs for scrolling text animation
const titleRef = ref<HTMLElement>()
const titleContainerRef = ref<HTMLElement>()
const shouldScroll = ref(false)

const resendActivationEmail = async () => {
  authStore.canResendActivationEmailAfter = 30
  resendActivationEmailInterval.value = setInterval(() => {
    authStore.canResendActivationEmailAfter--
    if (authStore.canResendActivationEmailAfter === 0) {
      clearInterval(resendActivationEmailInterval.value)
    }
  }, 1000)
  authStore.resendVerificationEmail()
}

// Check if title needs scrolling
const checkTitleOverflow = () => {
  if (titleRef.value && titleContainerRef.value) {
    const titleWidth = titleRef.value.scrollWidth
    const containerWidth = titleContainerRef.value.clientWidth
    shouldScroll.value = titleWidth > containerWidth
  }
}

onMounted(() => {
  if (authStore.canResendActivationEmailAfter > 0) {
    resendActivationEmailInterval.value = setInterval(() => {
      authStore.canResendActivationEmailAfter--
      if (authStore.canResendActivationEmailAfter === 0) {
        clearInterval(resendActivationEmailInterval.value)
      }
    }, 1000)
  }

  // Check title overflow after mount and on resize
  nextTick(() => {
    checkTitleOverflow()
  })

  window.addEventListener('resize', checkTitleOverflow)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkTitleOverflow)
})

// Watch for adsBanner changes to recheck overflow
watch(adsBanner, () => {
  nextTick(() => {
    checkTitleOverflow()
  })
}, { deep: true })
</script>

<template>
  <div class="fixed top-0 left-0 right-0 z-50">
    <UBanner
      v-if="isNotVerifyAccount"
      color="warning"
      :title="$t('auth.notVerifyAccount')"
      :actions="[
        {
          label:
            canResendActivationEmailAfter > 0
              ? `Resend in ${canResendActivationEmailAfter} seconds`
              : $t('auth.resendActivationEmail'),
          onClick: resendActivationEmail,
          disabled: canResendActivationEmailAfter > 0
        }
      ]"
      icon="i-lucide-info"
    />
  </div>
</template>

<style scoped>
.scrolling-text {
  animation: scroll-continuous 25s linear infinite;
  will-change: transform;
}

.scrolling-text:hover {
  animation-play-state: paused;
}

@keyframes scroll-continuous {
  0% {
    transform: translateX(20%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .scrolling-text {
    animation: none;
    transform: none;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
